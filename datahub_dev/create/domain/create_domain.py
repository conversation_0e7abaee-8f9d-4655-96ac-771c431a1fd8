# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/4 15:56
@Auth ： 李昌鑫
@File ：create_domain.py
@IDE ：PyCharm
@Motto：批量创建DataHub域
"""
from datahub_dev.config import api_token
import requests
import json
import time

class DataHubDomainCreator:
    def __init__(self, access_token="<my-access-token>"):
        self.url = "http://120.27.143.32:8080/api/graphql"
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        # 要创建的域列表 - 包含名称和描述
        self.domains = [
            {"name": "财务-fin"},
            {"name": "简道云特有业务-jdy"},
            {"name": "产品与研发-prod"},
            {"name": "运营-oprn"},
            {"name": "市场-mkt"},
            {"name": "IDP"},
            {"name": "客户-cust"},
            {"name": "公共数据-pub"},
            {"name": "产品-prod"},
            {"name": "行政-ad"},
            {"name": "客户服务与技术支持-ts"},
            {"name": "数仓治理-dw"},
            {"name": "人事-hr"},
            {"name": "销售过程-sales"}
        ]

    def create_domain(self, domain_name, domain_description):
        """创建单个域"""
        query = f"""
        mutation createDomain {{ 
            createDomain(input: {{ 
                name: "{domain_name}", 
                description: "{domain_description}" 
            }}) 
        }}
        """
        
        payload = {
            "query": query,
            "variables": {}
        }
        
        try:
            response = requests.post(
                self.url,
                headers=self.headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功创建域: {domain_name}")
                return True, result
            else:
                print(f"❌ 创建域失败: {domain_name}, 状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False, response.text
                
        except Exception as e:
            print(f"❌ 创建域时发生错误: {domain_name}, 错误: {str(e)}")
            return False, str(e)

    def create_all_domains(self):
        """批量创建所有域"""
        print(f"开始批量创建 {len(self.domains)} 个域...")
        print("=" * 50)
        
        success_count = 0
        failed_domains = []
        
        for i, domain in enumerate(self.domains, 1):
            domain_name = domain["name"]
            print(f"[{i}/{len(self.domains)}] 正在创建域: {domain_name}")
            
            success, result = self.create_domain(domain_name,None)
            
            if success:
                success_count += 1
            else:
                failed_domains.append(domain_name)
            
            # 添加延迟避免请求过快
            if i < len(self.domains):
                time.sleep(1)
        
        print("=" * 50)
        print(f"创建完成! 成功: {success_count}, 失败: {len(failed_domains)}")
        
        if failed_domains:
            print(f"失败的域: {', '.join(failed_domains)}")

def main():
    """主函数"""
    
    creator = DataHubDomainCreator(api_token)
    creator.create_all_domains()

if __name__ == "__main__":
    main()
