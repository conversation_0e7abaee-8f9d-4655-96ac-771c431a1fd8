# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/4 15:56
@Auth ： 李昌鑫
@File ：create_domain.py
@IDE ：PyCharm
@Motto：批量创建DataHub域，支持一级域和二级域
"""
from datahub_dev.config import api_token
import requests
import json
import time

class DataHubDomainCreator:
    def __init__(self, access_token="<my-access-token>"):
        self.url = "http://*************:8080/api/graphql"
        self.headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        # 一级域列表
        self.parent_domains = [
            {"name": "IDP", "description": "产研开发管理相关域"},
            {"name": "产品与研发-prod", "description": "产品与研发相关域"},
            {"name": "人事-hr", "description": "人事管理相关域"},
            {"name": "客户-cust", "description": "客户管理相关域"},
            {"name": "客户服务与技术支持-ts", "description": "客户服务与技术支持相关域"},
            {"name": "市场-mkt", "description": "市场营销相关域"},
            {"name": "数仓治理-dw", "description": "数仓治理相关域"},
            {"name": "简道云特有业务-jdy", "description": "简道云特有业务相关域"},
            {"name": "财务-fin", "description": "财务管理相关域"},
            {"name": "运营-oprn", "description": "运营管理相关域"},
            {"name": "销售过程-sales", "description": "销售过程管理相关域"}
        ]

        # 二级域映射关系 - 每个一级域对应的二级域列表
        self.subdomain_mapping = {
            "IDP": ["产研开发管理"],
            "产品与研发-prod": ["GTM", "IDP", "埋点", "需求与bug"],
            "人事-hr": ["人员组织"],
            "客户-cust": ["客户"],
            "客户服务与技术支持-ts": ["ITR", "客户信息"],
            "市场-mkt": ["活动"],
            "数仓治理-dw": ["指标监控"],
            "简道云特有业务-jdy": ["独享版"],
            "财务-fin": ["付款管理", "公共维度", "差旅平台处理", "开票管理", "税金管理", "账务管理", "资金管理"],
            "运营-oprn": ["售前运营", "学习赋能", "营销线整体", "销售运营", "项目运营"],
            "销售过程-sales": ["供应商合作", "合同", "商务记录", "商机", "线索", "项目实施"]
        }

    def create_domain(self, domain_name, domain_description):
        """创建单个域"""
        query = f"""
        mutation createDomain {{ 
            createDomain(input: {{ 
                name: "{domain_name}", 
                description: "{domain_description}" 
            }}) 
        }}
        """
        
        payload = {
            "query": query,
            "variables": {}
        }
        
        try:
            response = requests.post(
                self.url,
                headers=self.headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功创建域: {domain_name}")
                return True, result
            else:
                print(f"❌ 创建域失败: {domain_name}, 状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False, response.text
                
        except Exception as e:
            print(f"❌ 创建域时发生错误: {domain_name}, 错误: {str(e)}")
            return False, str(e)

    def create_all_domains(self):
        """批量创建所有域"""
        print(f"开始批量创建 {len(self.domains)} 个域...")
        print("=" * 50)
        
        success_count = 0
        failed_domains = []
        
        for i, domain in enumerate(self.domains, 1):
            domain_name = domain["name"]
            print(f"[{i}/{len(self.domains)}] 正在创建域: {domain_name}")
            
            success, result = self.create_domain(domain_name,None)
            
            if success:
                success_count += 1
            else:
                failed_domains.append(domain_name)
            
            # 添加延迟避免请求过快
            if i < len(self.domains):
                time.sleep(1)
        
        print("=" * 50)
        print(f"创建完成! 成功: {success_count}, 失败: {len(failed_domains)}")
        
        if failed_domains:
            print(f"失败的域: {', '.join(failed_domains)}")

def main():
    """主函数"""
    
    creator = DataHubDomainCreator(api_token)
    creator.create_all_domains()

if __name__ == "__main__":
    main()
