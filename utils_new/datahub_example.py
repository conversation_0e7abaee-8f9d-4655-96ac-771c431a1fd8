# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/19 
@Auth ： AI Assistant
@File ：datahub_example.py
@IDE ：PyCharm
@Motto：DataHub工具类使用示例
"""
from datahub_dev.config import api_token
from utils_new.datahub_client import DataHubClient, create_datahub_client_from_config, quick_delete_dataset, quick_delete_datasets_batch



def example_basic_usage():
    """
    基本使用示例
    """
    print("=== 基本使用示例 ===")
    
    # 方式1: 直接创建客户端
    client = DataHubClient(
        server_url="http://120.27.143.32:8080",
        api_token=api_token  # 替换为实际的token
    )
    
    # 删除单个数据集（软删除）
    # success = client.soft_delete_dataset("test_dataset", platform="hive")
    # print(f"软删除结果: {success}")
    
    # 删除单个数据集（硬删除）
    success = client.hard_delete_dataset("22754805-dbf1-45c4-b5c5-7f48e11723ab", platform="FineDataLink")
    print(f"硬删除结果: {success}")


def example_batch_delete():
    """
    批量删除示例
    """
    print("=== 批量删除示例 ===")
    
    # 从配置文件创建客户端
    client = create_datahub_client_from_config()
    
    # 要删除的数据集列表
    dataset_names = [
        "test_dataset_1",
        "test_dataset_2", 
        "test_dataset_3"
    ]
    
    # 批量软删除
    results = client.delete_datasets_batch(
        dataset_names=dataset_names,
        platform="hive",
        hard=False
    )
    
    print(f"批量删除结果: {results}")
    print(f"成功删除: {len(results['success'])} 个")
    print(f"删除失败: {len(results['failed'])} 个")


def example_different_platforms():
    """
    不同平台示例
    """
    print("=== 不同平台示例 ===")
    
    client = create_datahub_client_from_config()
    
    # 删除Hive数据集
    client.delete_dataset("hive_table", platform="hive")
    
    # 删除FineBI数据集
    client.delete_dataset("finebi_dataset", platform="FineBI")
    
    # 删除MySQL数据集
    client.delete_dataset("mysql_table", platform="mysql")


def example_quick_functions():
    """
    便捷函数示例
    """
    print("=== 便捷函数示例 ===")
    
    # 快速删除单个数据集
    success = quick_delete_dataset(
        dataset_name="quick_test_dataset",
        platform="hive",
        hard=False,
        server_url="http://120.27.143.32:8080",
        api_token="your_token"
    )
    print(f"快速删除结果: {success}")
    
    # 快速批量删除
    dataset_names = ["dataset1", "dataset2", "dataset3"]
    results = quick_delete_datasets_batch(
        dataset_names=dataset_names,
        platform="hive",
        hard=False,
        server_url="http://120.27.143.32:8080",
        api_token="your_token"
    )
    print(f"快速批量删除结果: {results}")


def example_real_case():
    """
    实际使用案例 - 参考原有的delete_crmbi_dataset.py
    """
    print("=== 实际使用案例 ===")
    
    # 从配置文件创建客户端
    client = create_datahub_client_from_config()
    
    # 实际要删除的数据集列表（来自原有代码）
    dataset_names = [
        "变更_机会变更记录_Cencil&Christal",
        "基础_合同&产品_订阅制合同_Cencil",
        "基础_回款&产品_订阅制合同回款_Cencil",
        "基础_日期&合同&产品线_ARR明细_Cencil",
        "基础_机会_准标杆_Cencil&Christal"
    ]
    
    # 批量软删除FineBI数据集
    results = client.delete_datasets_batch(
        dataset_names=dataset_names,
        platform="FineBI",
        hard=False
    )
    
    print(f"删除完成:")
    print(f"  总数: {results['total']}")
    print(f"  成功: {len(results['success'])}")
    print(f"  失败: {len(results['failed'])}")
    
    if results['failed']:
        print(f"  失败的数据集: {results['failed']}")


if __name__ == "__main__":
    # 运行示例（注释掉实际执行，避免误删除）
    print("DataHub工具类使用示例")
    print("注意: 实际使用时请确保配置正确，并谨慎删除数据集")
    
    example_basic_usage()
    # example_batch_delete()
    # example_different_platforms()
    # example_quick_functions()
    # example_real_case()
    
    print("示例代码已准备就绪，请根据需要取消注释并运行")
